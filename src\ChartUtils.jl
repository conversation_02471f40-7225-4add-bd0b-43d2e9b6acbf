#!/usr/bin/env julia

"""
圖表工具模組
提供統一的圖表繪製和數據視覺化功能
"""

module ChartUtils

using Dates, CSV, DataFrames, Statistics, Printf

export ChartData, create_chart_data, display_text_chart, display_ascii_chart
export format_price, format_percentage, format_volume

# 圖表數據結構
struct ChartData
    dates::Vector{Date}
    opens::Vector{Float64}
    highs::Vector{Float64}
    lows::Vector{Float64}
    closes::Vector{Float64}
    volumes::Vector{Int}
    ticker::String
    timeframe::String
end

"""
創建圖表數據結構
"""
function create_chart_data(df::DataFrame, ticker::String, timeframe::String)::ChartData
    return ChartData(
        df.date,
        df.open,
        df.high,
        df.low,
        df.close,
        df.volume,
        ticker,
        timeframe
    )
end

"""
格式化價格顯示
"""
function format_price(price::Float64)::String
    return @sprintf("%.4f", price)
end

"""
格式化百分比顯示
"""
function format_percentage(change::Float64)::String
    if change > 0
        return "↗ +$(round(change, digits=2))%"
    elseif change < 0
        return "↘ $(round(change, digits=2))%"
    else
        return "→ 0.00%"
    end
end

"""
格式化成交量顯示
"""
function format_volume(volume::Int)::String
    if volume >= 1_000_000
        return "$(round(volume/1_000_000, digits=1))M"
    elseif volume >= 1_000
        return "$(round(volume/1_000, digits=1))K"
    else
        return string(volume)
    end
end

"""
顯示文字蠟燭圖
"""
function display_text_chart(chart_data::ChartData, max_rows::Int=20)
    println("📊 $(chart_data.ticker) $(chart_data.timeframe)蠟燭圖")
    println("="^80)
    println("日期         開盤價    最高價    最低價    收盤價    漲跌      成交量")
    println("-"^80)
    
    n = length(chart_data.dates)
    start_idx = max(1, n - max_rows + 1)
    
    for i in start_idx:n
        date_str = string(chart_data.dates[i])
        open_str = format_price(chart_data.opens[i])
        high_str = format_price(chart_data.highs[i])
        low_str = format_price(chart_data.lows[i])
        close_str = format_price(chart_data.closes[i])
        volume_str = format_volume(chart_data.volumes[i])
        
        # 計算漲跌幅
        change_pct = if i > 1
            ((chart_data.closes[i] - chart_data.closes[i-1]) / chart_data.closes[i-1]) * 100
        else
            0.0
        end
        
        change_str = i > 1 ? format_percentage(change_pct) : "→ --"
        
        println("$(rpad(date_str, 12)) $(rpad(open_str, 8)) $(rpad(high_str, 8)) $(rpad(low_str, 8)) $(rpad(close_str, 8)) $(rpad(change_str, 8)) $(rpad(volume_str, 8))")
    end
end

"""
顯示ASCII價格走勢圖
"""
function display_ascii_chart(chart_data::ChartData, width::Int=60, height::Int=10, max_points::Int=60)
    println("\n📈 $(chart_data.ticker) $(chart_data.timeframe)價格走勢 (最近 $max_points 個數據點)")
    println("="^80)
    
    n = length(chart_data.closes)
    if n == 0
        println("❌ 沒有數據可顯示")
        return
    end
    
    # 選擇要顯示的數據點
    start_idx = max(1, n - max_points + 1)
    prices = chart_data.closes[start_idx:end]
    dates = chart_data.dates[start_idx:end]
    
    if length(prices) < 2
        println("❌ 數據點不足，無法繪製圖表")
        return
    end
    
    # 計算價格範圍
    min_price = minimum(prices)
    max_price = maximum(prices)
    price_range = max_price - min_price
    
    if price_range == 0
        println("❌ 價格無變化，無法繪製圖表")
        return
    end
    
    # 創建圖表網格
    chart_width = min(width, length(prices))
    chart_height = height
    
    # 計算每個價格對應的Y座標
    y_coords = Int[]
    for price in prices
        y = round(Int, (price - min_price) / price_range * (chart_height - 1)) + 1
        push!(y_coords, y)
    end
    
    # 繪製圖表
    for row in chart_height:-1:1
        # 計算當前行對應的價格
        current_price = min_price + (row - 1) / (chart_height - 1) * price_range
        price_label = @sprintf("%8.4f", current_price)
        print("$price_label │")
        
        # 繪製數據點
        for col in 1:min(chart_width, length(y_coords))
            if y_coords[col] == row
                # 判斷趨勢方向
                if col > 1
                    if prices[col] > prices[col-1]
                        print("▲")  # 上漲
                    elseif prices[col] < prices[col-1]
                        print("▼")  # 下跌
                    else
                        print("■")  # 持平
                    end
                else
                    print("■")  # 第一個點
                end
            else
                print(" ")
            end
        end
        println()
    end
    
    # 繪製底部邊框和標籤
    print("         └")
    for i in 1:chart_width
        print("─")
    end
    println()
    
    # 顯示日期範圍和統計信息
    if !isempty(dates)
        println("         $(dates[1]) 到 $(dates[end])")
        println("         最低: $(round(min_price, digits=1))  最高: $(round(max_price, digits=1))")
    end
end

"""
顯示統計信息
"""
function display_statistics(chart_data::ChartData)
    if isempty(chart_data.closes)
        println("❌ 沒有數據可分析")
        return
    end
    
    println("\n📊 $(chart_data.ticker) $(chart_data.timeframe) 統計信息：")
    println("-"^30)
    
    # 基本統計
    n = length(chart_data.closes)
    latest_price = chart_data.closes[end]
    highest_price = maximum(chart_data.closes)
    lowest_price = minimum(chart_data.closes)
    avg_price = mean(chart_data.closes)
    price_std = std(chart_data.closes)
    avg_volume = mean(chart_data.volumes)
    
    # 總變化
    total_change = latest_price - chart_data.closes[1]
    total_change_pct = (total_change / chart_data.closes[1]) * 100
    
    println("數據期間：$(chart_data.dates[1]) 到 $(chart_data.dates[end])")
    println("總期數：$n")
    println("最新價格：$(format_price(latest_price))")
    println("最高價格：$(format_price(highest_price))")
    println("最低價格：$(format_price(lowest_price))")
    println("平均價格：$(format_price(avg_price))")
    println("價格標準差：$(format_price(price_std))")
    println("平均成交量：$(format_volume(round(Int, avg_volume)))")
    println("總變化：$(format_price(total_change)) ($(round(total_change_pct, digits=2))%)")
end

"""
檢查Plots.jl可用性
"""
function check_plots_availability()::Bool
    try
        @eval using Plots
        return true
    catch
        return false
    end
end

"""
嘗試載入繪圖包
"""
function try_load_plotting_packages()::Bool
    try
        @eval begin
            using Plots
            plotlyjs()
        end
        println("✅ Plots.jl 已載入，支援專業圖表功能")
        return true
    catch e
        println("⚠️  Plots.jl 未安裝或載入失敗，僅支援文字版圖表")
        println("   安裝命令：julia --project=. -e \"using Pkg; Pkg.add(\\\"Plots\\\")\"")
        return false
    end
end

end # module ChartUtils
