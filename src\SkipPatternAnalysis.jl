#!/usr/bin/env julia

"""
Skip模式深度分析模組
實現Skip值的高級分析功能，包括規律發現、趨勢預測、策略優化等
"""

module SkipPatternAnalysis

using Dates, Statistics, Printf

# 導入需要的類型和函數
include("LotteryAnalysis.jl")
using .LotteryAnalysis: LotteryDraw, SkipAnalysisResult, analyze_skip_patterns, calculate_skip_values_for_draw

export discover_hidden_patterns, predict_number_trends, optimize_selection_strategy
export cross_validate_analysis, SkipTrendAnalysis, PatternDiscovery
export analyze_skip_cycles, detect_skip_anomalies, generate_skip_predictions

# Skip趨勢分析結果
struct SkipTrendAnalysis
    number::Int
    current_skip::Int
    predicted_next_skip::Float64
    trend_direction::String  # "上升", "下降", "穩定"
    confidence_level::Float64
    historical_pattern::Vector{Int}
    cycle_length::Union{Int,Nothing}
    anomaly_score::Float64
end

# 模式發現結果
struct PatternDiscovery
    pattern_type::String
    description::String
    frequency::Int
    confidence::Float64
    examples::Vector{Tuple{Int,Date,Vector{Int}}}  # (期數, 日期, Skip值)
    prediction_value::Float64
end

"""
1. 發現隱藏規律：分析Skip值的週期性和模式
"""
function discover_hidden_patterns(historical_data::Vector{LotteryDraw}, analysis_periods::Int=200)::Vector{PatternDiscovery}
    println("🔍 正在發現Skip值隱藏規律...")
    
    # 獲取Skip分析結果
    skip_results = analyze_skip_patterns(historical_data, analysis_periods)
    patterns = PatternDiscovery[]
    
    # 1. 週期性模式發現
    cycle_patterns = discover_cycle_patterns(skip_results)
    append!(patterns, cycle_patterns)
    
    # 2. 連續性模式發現
    sequence_patterns = discover_sequence_patterns(skip_results)
    append!(patterns, sequence_patterns)
    
    # 3. 極值模式發現
    extreme_patterns = discover_extreme_patterns(skip_results)
    append!(patterns, extreme_patterns)
    
    # 4. 相關性模式發現
    correlation_patterns = discover_correlation_patterns(skip_results)
    append!(patterns, correlation_patterns)
    
    println("✅ 發現 $(length(patterns)) 個隱藏模式")
    return patterns
end

"""
發現週期性模式
"""
function discover_cycle_patterns(skip_results::Vector{SkipAnalysisResult})::Vector{PatternDiscovery}
    patterns = PatternDiscovery[]
    
    # 分析總Skip值的週期性
    total_skips = [r.skip_statistics["total_skip"] for r in skip_results]
    
    # 檢測週期長度（3-20期）
    for cycle_len in 3:20
        if length(total_skips) < cycle_len * 3
            continue
        end
        
        # 計算週期相關性
        correlations = Float64[]
        for offset in 1:(length(total_skips) - cycle_len)
            if offset + cycle_len <= length(total_skips)
                corr = cor(total_skips[offset:offset+cycle_len-1], 
                          total_skips[offset+cycle_len:min(offset+2*cycle_len-1, length(total_skips))])
                if !isnan(corr)
                    push!(correlations, corr)
                end
            end
        end
        
        if !isempty(correlations)
            avg_corr = mean(correlations)
            if avg_corr > 0.3  # 顯著相關性
                examples = [(i, skip_results[i].draw_date, skip_results[i].skip_values) 
                           for i in 1:min(3, length(skip_results))]
                
                pattern = PatternDiscovery(
                    "週期性模式",
                    "總Skip值每 $(cycle_len) 期呈現週期性變化",
                    length(correlations),
                    avg_corr,
                    examples,
                    avg_corr * 100
                )
                push!(patterns, pattern)
            end
        end
    end
    
    return patterns
end

"""
發現連續性模式
"""
function discover_sequence_patterns(skip_results::Vector{SkipAnalysisResult})::Vector{PatternDiscovery}
    patterns = PatternDiscovery[]
    
    # 檢測連續低Skip模式
    low_skip_sequences = []
    current_sequence = 0
    
    for result in skip_results
        avg_skip = result.skip_statistics["avg_skip"]
        if avg_skip <= 3.0  # 低Skip閾值
            current_sequence += 1
        else
            if current_sequence >= 3
                push!(low_skip_sequences, current_sequence)
            end
            current_sequence = 0
        end
    end
    
    if current_sequence >= 3
        push!(low_skip_sequences, current_sequence)
    end
    
    if !isempty(low_skip_sequences)
        avg_length = mean(low_skip_sequences)
        frequency = length(low_skip_sequences)
        
        examples = [(i, skip_results[i].draw_date, skip_results[i].skip_values) 
                   for i in 1:min(3, length(skip_results)) 
                   if skip_results[i].skip_statistics["avg_skip"] <= 3.0]
        
        pattern = PatternDiscovery(
            "連續低Skip模式",
            "平均Skip值連續 $(round(avg_length, digits=1)) 期保持在3以下",
            frequency,
            min(1.0, frequency / 10),
            examples,
            avg_length * 10
        )
        push!(patterns, pattern)
    end
    
    return patterns
end

"""
發現極值模式
"""
function discover_extreme_patterns(skip_results::Vector{SkipAnalysisResult})::Vector{PatternDiscovery}
    patterns = PatternDiscovery[]
    
    # 收集所有Skip值
    all_skips = Int[]
    for result in skip_results
        append!(all_skips, result.skip_values)
    end
    
    if isempty(all_skips)
        return patterns
    end
    
    # 計算極值閾值
    skip_mean = mean(all_skips)
    skip_std = std(all_skips)
    high_threshold = skip_mean + 2 * skip_std
    
    # 檢測極高Skip後的反彈模式
    extreme_rebounds = []
    
    for i in 1:(length(skip_results)-1)
        current_max = maximum(skip_results[i].skip_values)
        if current_max > high_threshold
            next_avg = skip_results[i+1].skip_statistics["avg_skip"]
            if next_avg < skip_mean
                push!(extreme_rebounds, (current_max, next_avg))
            end
        end
    end
    
    if length(extreme_rebounds) >= 3
        rebound_rate = length(extreme_rebounds) / length(skip_results) * 100
        
        examples = [(i, skip_results[i].draw_date, skip_results[i].skip_values) 
                   for i in 1:min(3, length(skip_results)) 
                   if maximum(skip_results[i].skip_values) > high_threshold]
        
        pattern = PatternDiscovery(
            "極值反彈模式",
            "極高Skip值後下期平均Skip顯著降低",
            length(extreme_rebounds),
            rebound_rate / 100,
            examples,
            rebound_rate
        )
        push!(patterns, pattern)
    end
    
    return patterns
end

"""
發現相關性模式
"""
function discover_correlation_patterns(skip_results::Vector{SkipAnalysisResult})::Vector{PatternDiscovery}
    patterns = PatternDiscovery[]
    
    if length(skip_results) < 10
        return patterns
    end
    
    # 分析總Skip與範圍的相關性
    total_skips = [r.skip_statistics["total_skip"] for r in skip_results]
    skip_ranges = [r.skip_statistics["skip_range"] for r in skip_results]
    
    if length(total_skips) == length(skip_ranges) && length(total_skips) > 5
        correlation = cor(total_skips, skip_ranges)
        
        if !isnan(correlation) && abs(correlation) > 0.5
            direction = correlation > 0 ? "正相關" : "負相關"
            
            examples = [(i, skip_results[i].draw_date, skip_results[i].skip_values) 
                       for i in 1:min(3, length(skip_results))]
            
            pattern = PatternDiscovery(
                "總Skip-範圍相關性",
                "總Skip值與Skip範圍呈現$(direction) (r=$(round(correlation, digits=3)))",
                length(skip_results),
                abs(correlation),
                examples,
                abs(correlation) * 100
            )
            push!(patterns, pattern)
        end
    end
    
    return patterns
end

"""
2. 提供預測線索：基於Skip值變化預測號碼趨勢
"""
function predict_number_trends(historical_data::Vector{LotteryDraw}, prediction_periods::Int=50)::Vector{SkipTrendAnalysis}
    println("🔮 正在分析號碼Skip趨勢...")
    
    trends = SkipTrendAnalysis[]
    
    for number in 1:39
        trend = analyze_single_number_trend(number, historical_data, prediction_periods)
        if trend !== nothing
            push!(trends, trend)
        end
    end
    
    # 按預測信心度排序
    sort!(trends, by=t -> t.confidence_level, rev=true)
    
    println("✅ 完成39個號碼的趨勢分析")
    return trends
end

"""
分析單個號碼的Skip趨勢
"""
function analyze_single_number_trend(number::Int, historical_data::Vector{LotteryDraw}, periods::Int)::Union{SkipTrendAnalysis,Nothing}
    # 收集該號碼的Skip序列
    skip_sequence = Int[]
    current_skip = 0
    
    for (i, draw) in enumerate(historical_data[1:min(periods, length(historical_data))])
        if number in draw.numbers
            push!(skip_sequence, current_skip)
            current_skip = 0
        else
            current_skip += 1
        end
    end
    
    if length(skip_sequence) < 5
        return nothing
    end
    
    # 分析趨勢
    recent_skips = skip_sequence[1:min(5, length(skip_sequence))]
    older_skips = skip_sequence[max(1, length(skip_sequence)-9):end]
    
    recent_avg = mean(recent_skips)
    older_avg = mean(older_skips)
    
    # 判斷趨勢方向
    trend_direction = if recent_avg > older_avg * 1.2
        "上升"
    elseif recent_avg < older_avg * 0.8
        "下降"
    else
        "穩定"
    end
    
    # 預測下次Skip值
    predicted_skip = if length(skip_sequence) >= 3
        # 使用移動平均預測
        weights = [0.5, 0.3, 0.2]
        sum(skip_sequence[1:3] .* weights)
    else
        mean(skip_sequence)
    end
    
    # 計算信心度
    skip_variance = var(skip_sequence)
    confidence = max(0.1, min(0.9, 1.0 / (1.0 + skip_variance / 10)))
    
    # 檢測週期
    cycle_length = detect_cycle_length(skip_sequence)
    
    # 計算異常分數
    anomaly_score = calculate_anomaly_score(skip_sequence, current_skip)
    
    return SkipTrendAnalysis(
        number,
        current_skip,
        predicted_skip,
        trend_direction,
        confidence,
        skip_sequence[1:min(10, length(skip_sequence))],
        cycle_length,
        anomaly_score
    )
end

"""
檢測週期長度
"""
function detect_cycle_length(sequence::Vector{Int})::Union{Int,Nothing}
    if length(sequence) < 6
        return nothing
    end
    
    best_cycle = nothing
    best_score = 0.0
    
    for cycle_len in 2:min(10, div(length(sequence), 2))
        score = 0.0
        count = 0
        
        for i in 1:(length(sequence) - cycle_len)
            if i + cycle_len <= length(sequence)
                if sequence[i] == sequence[i + cycle_len]
                    score += 1.0
                end
                count += 1
            end
        end
        
        if count > 0
            cycle_score = score / count
            if cycle_score > best_score && cycle_score > 0.4
                best_score = cycle_score
                best_cycle = cycle_len
            end
        end
    end
    
    return best_cycle
end

"""
計算異常分數
"""
function calculate_anomaly_score(sequence::Vector{Int}, current_skip::Int)::Float64
    if isempty(sequence)
        return 0.5
    end
    
    seq_mean = mean(sequence)
    seq_std = std(sequence)
    
    if seq_std == 0
        return current_skip == seq_mean ? 0.0 : 1.0
    end
    
    z_score = abs(current_skip - seq_mean) / seq_std
    return min(1.0, z_score / 3.0)  # 標準化到0-1
end

"""
3. 優化選號策略：結合Skip分析制定投注策略
"""
function optimize_selection_strategy(historical_data::Vector{LotteryDraw}, strategy_type::String="balanced")::Dict{String,Any}
    println("🎯 正在優化選號策略...")
    
    # 獲取趨勢分析
    trends = predict_number_trends(historical_data, 100)
    
    # 獲取模式發現
    patterns = discover_hidden_patterns(historical_data, 150)
    
    strategy = Dict{String,Any}()
    
    if strategy_type == "aggressive"
        strategy = create_aggressive_strategy(trends, patterns)
    elseif strategy_type == "conservative"
        strategy = create_conservative_strategy(trends, patterns)
    else  # balanced
        strategy = create_balanced_strategy(trends, patterns)
    end
    
    println("✅ $(strategy_type)策略優化完成")
    return strategy
end

"""
創建激進策略
"""
function create_aggressive_strategy(trends::Vector{SkipTrendAnalysis}, patterns::Vector{PatternDiscovery})::Dict{String,Any}
    # 選擇高異常分數和下降趨勢的號碼
    hot_numbers = [t.number for t in trends if t.anomaly_score > 0.7 || t.trend_direction == "下降"]
    hot_numbers = hot_numbers[1:min(15, length(hot_numbers))]
    
    # 選擇低Skip值號碼
    low_skip_numbers = [t.number for t in trends if t.current_skip <= 2]
    
    strategy = Dict{String,Any}(
        "strategy_type" => "激進策略",
        "description" => "重點關注異常高Skip和下降趨勢號碼",
        "primary_numbers" => hot_numbers,
        "secondary_numbers" => low_skip_numbers,
        "risk_level" => "高",
        "expected_hit_rate" => "30-40%",
        "recommendation" => "適合短期投注，追求高回報"
    )
    
    return strategy
end

"""
創建保守策略
"""
function create_conservative_strategy(trends::Vector{SkipTrendAnalysis}, patterns::Vector{PatternDiscovery})::Dict{String,Any}
    # 選擇穩定趨勢和中等Skip值的號碼
    stable_numbers = [t.number for t in trends if t.trend_direction == "穩定" && t.confidence_level > 0.6]
    stable_numbers = stable_numbers[1:min(15, length(stable_numbers))]
    
    # 選擇中等Skip值號碼
    medium_skip_numbers = [t.number for t in trends if 3 <= t.current_skip <= 8]
    
    strategy = Dict{String,Any}(
        "strategy_type" => "保守策略",
        "description" => "選擇穩定趨勢和中等Skip值號碼",
        "primary_numbers" => stable_numbers,
        "secondary_numbers" => medium_skip_numbers,
        "risk_level" => "低",
        "expected_hit_rate" => "15-25%",
        "recommendation" => "適合長期投注，追求穩定回報"
    )
    
    return strategy
end

"""
創建平衡策略
"""
function create_balanced_strategy(trends::Vector{SkipTrendAnalysis}, patterns::Vector{PatternDiscovery})::Dict{String,Any}
    # 綜合考慮多個因素
    scored_numbers = []
    
    for trend in trends
        score = 0.0
        
        # Skip值評分
        if trend.current_skip <= 3
            score += 30
        elseif trend.current_skip <= 7
            score += 20
        else
            score += 10
        end
        
        # 趨勢評分
        if trend.trend_direction == "下降"
            score += 25
        elseif trend.trend_direction == "穩定"
            score += 15
        end
        
        # 信心度評分
        score += trend.confidence_level * 20
        
        # 異常分數評分
        if 0.3 <= trend.anomaly_score <= 0.7
            score += 15
        end
        
        push!(scored_numbers, (trend.number, score))
    end
    
    # 排序並選擇前15名
    sort!(scored_numbers, by=x -> x[2], rev=true)
    primary_numbers = [num for (num, score) in scored_numbers[1:min(15, length(scored_numbers))]]
    
    strategy = Dict{String,Any}(
        "strategy_type" => "平衡策略",
        "description" => "綜合Skip值、趨勢、信心度等多因素評分",
        "primary_numbers" => primary_numbers,
        "scoring_details" => scored_numbers[1:min(10, length(scored_numbers))],
        "risk_level" => "中",
        "expected_hit_rate" => "20-30%",
        "recommendation" => "適合大多數投注者，平衡風險與回報"
    )
    
    return strategy
end

"""
4. 驗證分析結果：為其他分析方法提供交叉驗證
"""
function cross_validate_analysis(historical_data::Vector{LotteryDraw}, validation_periods::Int=100)::Dict{String,Any}
    println("🔬 正在進行交叉驗證分析...")

    # 分割數據：前80%用於分析，後20%用於驗證
    split_point = div(length(historical_data) * 4, 5)
    training_data = historical_data[1:split_point]
    validation_data = historical_data[split_point+1:end]

    validation_results = Dict{String,Any}()

    # 1. Skip預測準確性驗證
    skip_accuracy = validate_skip_predictions(training_data, validation_data)
    validation_results["skip_prediction_accuracy"] = skip_accuracy

    # 2. 趨勢預測驗證
    trend_accuracy = validate_trend_predictions(training_data, validation_data)
    validation_results["trend_prediction_accuracy"] = trend_accuracy

    # 3. 策略效果驗證
    strategy_performance = validate_strategy_performance(training_data, validation_data)
    validation_results["strategy_performance"] = strategy_performance

    # 4. 與傳統分析方法比較
    comparison_results = compare_with_traditional_analysis(training_data, validation_data)
    validation_results["traditional_comparison"] = comparison_results

    println("✅ 交叉驗證完成")
    return validation_results
end

"""
驗證Skip預測準確性
"""
function validate_skip_predictions(training_data::Vector{LotteryDraw}, validation_data::Vector{LotteryDraw})::Dict{String,Any}
    # 基於訓練數據預測
    trends = predict_number_trends(training_data, length(training_data))

    # 在驗證數據中檢查預測準確性
    correct_predictions = 0
    total_predictions = 0
    prediction_errors = Float64[]

    for trend in trends
        number = trend.number
        predicted_skip = trend.predicted_next_skip

        # 在驗證數據中找到該號碼的實際Skip值
        actual_skip = calculate_actual_skip_in_validation(number, validation_data)

        if actual_skip !== nothing
            error = abs(predicted_skip - actual_skip)
            push!(prediction_errors, error)

            # 如果預測誤差在合理範圍內（±2），認為預測正確
            if error <= 2.0
                correct_predictions += 1
            end
            total_predictions += 1
        end
    end

    accuracy_rate = total_predictions > 0 ? correct_predictions / total_predictions : 0.0
    avg_error = isempty(prediction_errors) ? 0.0 : mean(prediction_errors)

    return Dict{String,Any}(
        "accuracy_rate" => round(accuracy_rate * 100, digits=1),
        "average_error" => round(avg_error, digits=2),
        "total_predictions" => total_predictions,
        "correct_predictions" => correct_predictions
    )
end

"""
計算驗證數據中的實際Skip值
"""
function calculate_actual_skip_in_validation(number::Int, validation_data::Vector{LotteryDraw})::Union{Int,Nothing}
    for (i, draw) in enumerate(validation_data)
        if number in draw.numbers
            return i - 1  # Skip值是距離開始的期數
        end
    end
    return nothing
end

"""
驗證趨勢預測
"""
function validate_trend_predictions(training_data::Vector{LotteryDraw}, validation_data::Vector{LotteryDraw})::Dict{String,Any}
    trends = predict_number_trends(training_data, length(training_data))

    correct_trends = 0
    total_trends = 0

    for trend in trends
        if trend.confidence_level > 0.5  # 只驗證高信心度預測
            number = trend.number
            predicted_direction = trend.trend_direction

            # 計算驗證期間的實際趨勢
            actual_direction = calculate_actual_trend_in_validation(number, training_data, validation_data)

            if actual_direction !== nothing
                if predicted_direction == actual_direction
                    correct_trends += 1
                end
                total_trends += 1
            end
        end
    end

    trend_accuracy = total_trends > 0 ? correct_trends / total_trends : 0.0

    return Dict{String,Any}(
        "trend_accuracy_rate" => round(trend_accuracy * 100, digits=1),
        "correct_trends" => correct_trends,
        "total_trends" => total_trends
    )
end

"""
計算驗證期間的實際趨勢
"""
function calculate_actual_trend_in_validation(number::Int, training_data::Vector{LotteryDraw}, validation_data::Vector{LotteryDraw})::Union{String,Nothing}
    # 計算訓練期末的Skip值
    training_skip = calculate_skip_values_for_draw(1, training_data)[findfirst(x -> x == number, training_data[1].numbers)]
    if training_skip === nothing
        return nothing
    end

    # 計算驗證期的平均Skip值
    validation_skips = Int[]
    current_skip = 0

    for draw in validation_data
        if number in draw.numbers
            push!(validation_skips, current_skip)
            current_skip = 0
        else
            current_skip += 1
        end
    end

    if isempty(validation_skips)
        return nothing
    end

    validation_avg = mean(validation_skips)

    # 判斷趨勢
    if validation_avg > training_skip * 1.2
        return "上升"
    elseif validation_avg < training_skip * 0.8
        return "下降"
    else
        return "穩定"
    end
end

"""
驗證策略效果
"""
function validate_strategy_performance(training_data::Vector{LotteryDraw}, validation_data::Vector{LotteryDraw})::Dict{String,Any}
    # 生成三種策略
    strategies = Dict{String,Any}()
    strategies["aggressive"] = optimize_selection_strategy(training_data, "aggressive")
    strategies["conservative"] = optimize_selection_strategy(training_data, "conservative")
    strategies["balanced"] = optimize_selection_strategy(training_data, "balanced")

    performance = Dict{String,Any}()

    for (strategy_name, strategy) in strategies
        primary_numbers = strategy["primary_numbers"]

        # 計算在驗證數據中的命中率
        hits = 0
        total_draws = length(validation_data)

        for draw in validation_data
            hit_count = length(intersect(draw.numbers, primary_numbers))
            if hit_count >= 2  # 至少命中2個號碼
                hits += 1
            end
        end

        hit_rate = total_draws > 0 ? hits / total_draws : 0.0

        performance[strategy_name] = Dict{String,Any}(
            "hit_rate" => round(hit_rate * 100, digits=1),
            "hits" => hits,
            "total_draws" => total_draws,
            "recommended_numbers" => primary_numbers[1:min(10, length(primary_numbers))]
        )
    end

    return performance
end

"""
與傳統分析方法比較
"""
function compare_with_traditional_analysis(training_data::Vector{LotteryDraw}, validation_data::Vector{LotteryDraw})::Dict{String,Any}
    # 傳統頻率分析
    frequency_analysis = traditional_frequency_analysis(training_data)

    # Skip分析
    skip_analysis = optimize_selection_strategy(training_data, "balanced")

    # 在驗證數據中比較效果
    freq_performance = test_number_set_performance(frequency_analysis, validation_data)
    skip_performance = test_number_set_performance(skip_analysis["primary_numbers"], validation_data)

    return Dict{String,Any}(
        "frequency_analysis" => Dict{String,Any}(
            "method" => "傳統頻率分析",
            "hit_rate" => freq_performance,
            "description" => "基於出現頻率選擇號碼"
        ),
        "skip_analysis" => Dict{String,Any}(
            "method" => "Skip值分析",
            "hit_rate" => skip_performance,
            "description" => "基於Skip模式選擇號碼"
        ),
        "improvement" => round((skip_performance - freq_performance) * 100, digits=1)
    )
end

"""
傳統頻率分析
"""
function traditional_frequency_analysis(historical_data::Vector{LotteryDraw})::Vector{Int}
    frequency_count = Dict{Int,Int}()

    for draw in historical_data
        for number in draw.numbers
            frequency_count[number] = get(frequency_count, number, 0) + 1
        end
    end

    # 按頻率排序，選擇前15名
    sorted_numbers = sort(collect(frequency_count), by=x -> x[2], rev=true)
    return [num for (num, count) in sorted_numbers[1:min(15, length(sorted_numbers))]]
end

"""
測試號碼集合的表現
"""
function test_number_set_performance(number_set::Vector{Int}, validation_data::Vector{LotteryDraw})::Float64
    hits = 0
    total_draws = length(validation_data)

    for draw in validation_data
        hit_count = length(intersect(draw.numbers, number_set))
        if hit_count >= 2  # 至少命中2個號碼
            hits += 1
        end
    end

    return total_draws > 0 ? hits / total_draws : 0.0
end

end # module SkipPatternAnalysis
