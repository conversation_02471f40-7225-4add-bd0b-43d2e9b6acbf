name = "AmibrokerChartSimilator"
uuid = "bd908015-4d49-4198-a913-9616b7886ab5"
authors = ["w0009 "]
version = "0.1.0"

[deps]
CSV = "336ed68f-0bac-5ca0-87d4-7b16caf5d00b"
DataFrames = "a93c6f00-e57d-5684-b7b6-d8193f3e46c0"
Dates = "ade2ca70-3891-5945-98fb-dc099432e06a"
PlotlyJS = "f0f68f2c-4968-5e81-91da-67840de0976a"
Plots = "91a5bcdd-55d7-5caf-9e0b-520d859cae80"
Printf = "de0858da-6303-5e67-8744-51eddeeeb8d7"
Random = "9a3f8284-a2c9-5f02-9a11-845980a1fd5c"
Statistics = "10745b16-79ce-11e8-11f9-7d13ad32a3b2"
StatsPlots = "f3b207a7-027a-5e70-b257-86293d7955fd"

[compat]
CSV = "0.10.15"
DataFrames = "1.7.0"
Dates = "1.11.0"
PlotlyJS = "0.18.16"
Plots = "1.40.17"
Printf = "1.11.0"
Random = "1.11.0"
StatsPlots = "0.15.7"
