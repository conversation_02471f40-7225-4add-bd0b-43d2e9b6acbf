# Project Structure

## Root Directory Organization

```
              # Kiro IDE configuration
```

## Source Code (`src/`)


## Data Structure (`data/`)
- **`fan5.csv`** - Historical lottery data in format: `YYYY-MM-DD,n1,n2,n3,n4,n5`
- **`*.jls`** - Serialized combination data for analysis

## Generated Data (`data_amibroker/`)
- **`fan5/g1/`** - Single number analysis files
- **`fan5/g2/`** - Two-number combination analysis
- **`fan5/g3/`** - Three-number combination analysis  
- **`fan5/g5/`** - Five-number combination analysis
- **`fan5/g7/`** - Seven-number combination analysis
- **`fan5/g2_10/`** - Ten pairs analysis

## Documentation (`doc/`)


## Key Data Structures


## Naming Conventions

## File Processing Flow
1. Load historical data from `data/fan5.csv`
2. Generate Amibroker files using `A0018_amibrokerprice_standalone.jl`
3. Output analysis files to `data_amibroker/` subdirectories