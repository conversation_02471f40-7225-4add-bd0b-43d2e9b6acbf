# Technology Stack

## Primary Language
- **Julia 1.6+** (recommended 1.8+) - Main implementation language for mathematical computations and data analysis

## Core Dependencies
- **CSV.jl** - CSV file parsing for lottery data
- **DataFrames.jl** - Data manipulation and analysis
- **Dates.jl** - Date handling for lottery draw dates
- **Random.jl** - Random number generation for testing
- **Combinatorics.jl** - Mathematical combinations for lottery analysis
- **StatsBase.jl** - Statistical functions
- **Serialization.jl** - Data persistence

## Data Format
- **Input**: CSV files with format `YYYY-MM-DD,num1,num2,num3,num4,num5`
- **Numbers**: Range 1-39 for Pick 5 from 39 lottery
- **Output**: Amibroker-compatible CSV files for technical analysis

## Common Commands

### Running the System
```bash

```

### System Operations
```julia

```

### Data Generation
```julia
# Generate Amibroker data files
include("src/A0018_amibrokerprice_standalone.jl")
main()  # Generates various combination analyses
```

## Development Environment
- **OS Support**: Windows, macOS, Linux
- **Memory**: Minimum 2GB RAM
- **Storage**: 100MB for data and analysis files

## File Extensions
- `.jl` - Julia source files
- `.csv` - Data files (input lottery data, output analysis)
- `.jls` - Serialized Julia objects for data persistence
- `.md` - Documentation files