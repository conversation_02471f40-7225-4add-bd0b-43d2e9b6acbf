#!/usr/bin/env julia

"""
AmiBroker圖表模擬器與Wonder Grid彩票分析系統 - 主程式
整合所有功能模組，提供統一的用戶界面
"""

# 載入自定義模組
include("LotteryAnalysis.jl")
include("ChartUtils.jl")
include("SkipPatternAnalysis.jl")

using .LotteryAnalysis
using .ChartUtils
using .SkipPatternAnalysis
using Printf

"""
主選單系統
"""
function main_menu()
    println("🎯 AmiBroker圖表模擬器與Wonder Grid分析系統")
    println("="^50)
    
    while true
        println("\n主選單：")
        println("1. Wonder Grid 彩票分析")
        println("2. 號碼統計分析")
        println("3. 有利號碼分析")
        println("4. 配對頻率分析")
        println("5. 🆕 Skip值分析")
        println("6. 🧠 Skip深度分析")
        println("7. 圖表模擬器")
        println("8. 系統自檢")
        println("9. 退出")
        
        print("\n請選擇 (1-9): ")
        choice = strip(readline())

        try
            if choice == "1"
                wonder_grid_analysis()
            elseif choice == "2"
                number_statistics_menu()
            elseif choice == "3"
                favorable_numbers_menu()
            elseif choice == "4"
                pairing_analysis_menu()
            elseif choice == "5"
                skip_analysis_menu()
            elseif choice == "6"
                skip_deep_analysis_menu()
            elseif choice == "7"
                chart_simulator_menu()
            elseif choice == "8"
                system_check()
            elseif choice == "9"
                println("👋 再見！")
                break
            else
                println("❌ 無效選擇，請輸入 1-9")
            end
        catch e
            println("❌ 發生錯誤：$e")
        end
    end
end

"""
Wonder Grid 分析
"""
function wonder_grid_analysis()
    println("\n🎯 Wonder Grid 彩票分析")
    println("-"^30)
    
    # 載入數據
    historical_data = load_real_data()
    if isempty(historical_data)
        println("❌ 無法載入數據")
        return
    end
    
    # 獲取關鍵號碼
    print("請輸入關鍵號碼 (1-39)，或按 Enter 使用預設值 13: ")
    key_input = strip(readline())
    
    key_number = if isempty(key_input)
        13
    else
        try
            num = parse(Int, key_input)
            (1 <= num <= 39) ? num : 13
        catch
            13
        end
    end
    
    println("使用關鍵號碼：$key_number")
    
    # 分析關鍵號碼
    stats = analyze_number_statistics(key_number, historical_data)
    display_number_stats(key_number, stats)
    
    # 生成組合
    combinations = generate_wonder_grid_combinations(key_number, 30)
    display_combinations(combinations, "Wonder Grid 組合 (關鍵號碼: $key_number)")
    
    # 顯示有利號碼分析
    display_favorable_analysis(historical_data)
end

"""
號碼統計分析選單
"""
function number_statistics_menu()
    println("\n📈 號碼統計分析")
    println("-"^20)

    historical_data = load_real_data()
    if isempty(historical_data)
        println("❌ 無法載入數據")
        return
    end

    println("總共有 $(length(historical_data)) 期數據可供分析")

    # 獲取分析範圍
    print("請選擇分析範圍：\n")
    print("1. 全部數據 ($(length(historical_data)) 期)\n")
    print("2. 最近N期數據\n")
    print("請選擇 (1-2): ")

    range_choice = strip(readline())
    selected_row = 1  # 默認從最新期開始
    n_periods = nothing  # 默認全部數據

    if range_choice == "2"
        print("請輸入要分析的期數 (1-$(length(historical_data))): ")
        periods_input = strip(readline())
        try
            n_periods = parse(Int, periods_input)
            if n_periods < 1 || n_periods > length(historical_data)
                println("❌ 期數必須在 1-$(length(historical_data)) 之間，使用全部數據")
                n_periods = nothing
            else
                println("✅ 將分析最近 $n_periods 期數據")
            end
        catch
            println("❌ 無效輸入，使用全部數據")
            n_periods = nothing
        end
    end

    print("請輸入要分析的號碼 (1-39): ")
    number_input = strip(readline())

    try
        number = parse(Int, number_input)
        if 1 <= number <= 39
            stats = analyze_number_statistics_range(number, historical_data, selected_row, n_periods)
            display_number_stats_range(number, stats)
        else
            println("❌ 號碼必須在 1-39 之間")
        end
    catch
        println("❌ 無效的號碼輸入")
    end
end

"""
有利號碼分析選單
"""
function favorable_numbers_menu()
    println("\n🎯 有利號碼分析")
    println("-"^20)

    historical_data = load_real_data()
    if isempty(historical_data)
        println("❌ 無法載入數據")
        return
    end

    println("總共有 $(length(historical_data)) 期數據可供分析")

    # 獲取分析範圍
    print("請選擇分析範圍：\n")
    print("1. 全部數據 ($(length(historical_data)) 期)\n")
    print("2. 最近N期數據\n")
    print("請選擇 (1-2): ")

    range_choice = strip(readline())
    selected_row = 1
    n_periods = nothing

    if range_choice == "2"
        print("請輸入要分析的期數 (1-$(length(historical_data))): ")
        periods_input = strip(readline())
        try
            n_periods = parse(Int, periods_input)
            if n_periods < 1 || n_periods > length(historical_data)
                println("❌ 期數必須在 1-$(length(historical_data)) 之間，使用全部數據")
                n_periods = nothing
            else
                println("✅ 將分析最近 $n_periods 期數據")
            end
        catch
            println("❌ 無效輸入，使用全部數據")
            n_periods = nothing
        end
    end

    display_favorable_analysis_range(historical_data, selected_row, n_periods)
end

"""
配對頻率分析選單
"""
function pairing_analysis_menu()
    println("\n🔗 配對頻率分析")
    println("-"^20)

    historical_data = load_real_data()
    if isempty(historical_data)
        println("❌ 無法載入數據")
        return
    end

    print("請輸入要分析配對的號碼 (1-39): ")
    number_input = strip(readline())

    try
        number = parse(Int, number_input)
        if 1 <= number <= 39
            # 這裡需要實現配對分析功能
            println("配對分析功能開發中...")
        else
            println("❌ 號碼必須在 1-39 之間")
        end
    catch
        println("❌ 無效的號碼輸入")
    end
end

"""
Skip值分析選單
"""
function skip_analysis_menu()
    println("\n🔢 Skip值分析")
    println("-"^15)

    historical_data = load_real_data()
    if isempty(historical_data)
        println("❌ 無法載入數據")
        return
    end

    println("總共有 $(length(historical_data)) 期數據可供分析")

    # 選擇分析模式
    println("\n請選擇分析模式：")
    println("1. 單期Skip值查詢")
    println("2. Skip模式分析 (分析多期)")
    println("3. Skip統計摘要")
    print("請選擇 (1-3): ")

    mode_choice = strip(readline())

    if mode_choice == "1"
        single_period_skip_analysis(historical_data)
    elseif mode_choice == "2"
        multi_period_skip_analysis(historical_data)
    elseif mode_choice == "3"
        skip_statistics_summary(historical_data)
    else
        println("❌ 無效選擇")
    end
end

"""
單期Skip值查詢
"""
function single_period_skip_analysis(historical_data::Vector{LotteryDraw})
    println("\n🔍 單期Skip值查詢")
    println("-"^20)

    print("請輸入要查詢的期數 (1為最新期，$(length(historical_data))為最早期): ")
    period_input = strip(readline())

    try
        period = parse(Int, period_input)
        if period < 1 || period > length(historical_data)
            println("❌ 期數必須在 1-$(length(historical_data)) 之間")
            return
        end

        if period == length(historical_data)
            println("❌ 最早期無法計算Skip值（沒有更早的歷史數據）")
            return
        end

        # 計算Skip值
        draw = historical_data[period]
        skip_values = calculate_skip_values_for_draw(period, historical_data)

        println("\n📊 第 $period 期 Skip值分析")
        println("日期：$(draw.draw_date)")
        println("開獎號碼：$(join([lpad(n, 2) for n in draw.numbers], " "))")
        println("Skip值：  $(join([lpad(s, 2) for s in skip_values], " "))")

        # 計算統計
        total_skip = sum(skip_values)
        avg_skip = round(total_skip / length(skip_values), digits=2)
        min_skip = minimum(skip_values)
        max_skip = maximum(skip_values)
        skip_range = max_skip - min_skip

        println("\n統計摘要：")
        println("  總Skip值：$(total_skip)")
        println("  平均Skip：$(avg_skip)")
        println("  Skip範圍：$(min_skip) - $(max_skip) (範圍：$(skip_range))")

        # 分析每個號碼的Skip特徵
        println("\n詳細分析：")
        for (i, (number, skip)) in enumerate(zip(draw.numbers, skip_values))
            if skip == 0
                println("  號碼 $(number)：Skip=$(skip) (連續開出)")
            elseif skip <= 2
                println("  號碼 $(number)：Skip=$(skip) (短期重現)")
            elseif skip <= 5
                println("  號碼 $(number)：Skip=$(skip) (中期重現)")
            else
                println("  號碼 $(number)：Skip=$(skip) (長期重現)")
            end
        end

    catch
        println("❌ 無效的期數輸入")
    end
end

"""
多期Skip模式分析
"""
function multi_period_skip_analysis(historical_data::Vector{LotteryDraw})
    println("\n📈 多期Skip模式分析")
    println("-"^20)

    print("請輸入要分析的期數 (建議不超過100期): ")
    periods_input = strip(readline())

    try
        periods = parse(Int, periods_input)
        if periods < 1
            println("❌ 期數必須大於 0")
            return
        end

        max_periods = min(periods, length(historical_data) - 1, 100)
        if max_periods != periods
            println("⚠️  調整分析期數為 $(max_periods) 期")
        end

        # 執行Skip分析
        skip_results = analyze_skip_patterns(historical_data, max_periods)

        if !isempty(skip_results)
            display_skip_analysis(skip_results, min(20, max_periods))
        else
            println("❌ Skip分析失敗")
        end

    catch
        println("❌ 無效的期數輸入")
    end
end

"""
Skip統計摘要
"""
function skip_statistics_summary(historical_data::Vector{LotteryDraw})
    println("\n📊 Skip統計摘要")
    println("-"^15)

    print("請輸入統計期數 (建議50-200期): ")
    periods_input = strip(readline())

    try
        periods = parse(Int, periods_input)
        if periods < 1
            println("❌ 期數必須大於 0")
            return
        end

        max_periods = min(periods, length(historical_data) - 1)
        if max_periods != periods
            println("⚠️  調整統計期數為 $(max_periods) 期")
        end

        # 執行Skip分析
        skip_results = analyze_skip_patterns(historical_data, max_periods)

        if !isempty(skip_results)
            stats = get_skip_statistics(skip_results)

            println("\n📈 Skip統計摘要 (基於 $(max_periods) 期數據)")
            println("="^50)

            println("🔢 Skip值分佈：")
            println("  最小值：$(stats["skip_min"])")
            println("  最大值：$(stats["skip_max"])")
            println("  平均值：$(stats["skip_mean"])")
            println("  中位數：$(stats["skip_median"])")
            println("  標準差：$(stats["skip_std"])")

            println("\n📊 每期總Skip統計：")
            println("  最小總Skip：$(stats["total_skip_min"])")
            println("  最大總Skip：$(stats["total_skip_max"])")
            println("  平均總Skip：$(stats["total_skip_mean"])")
            println("  中位數總Skip：$(stats["total_skip_median"])")

            println("\n🎯 Skip範圍統計：")
            println("  最小範圍：$(stats["range_min"])")
            println("  最大範圍：$(stats["range_max"])")
            println("  平均範圍：$(stats["range_mean"])")

            println("\n🔥 最常見Skip值：")
            for (i, (skip, count)) in enumerate(stats["most_common_skip"][1:min(10, length(stats["most_common_skip"]))])
                percentage = round(count / stats["total_skip_values"] * 100, digits=1)
                println("  第$(i)名：Skip=$skip (出現 $count 次，佔 $percentage%)")
            end

        else
            println("❌ Skip統計分析失敗")
        end

    catch
        println("❌ 無效的期數輸入")
    end
end

"""
圖表模擬器選單
"""
function chart_simulator_menu()
    println("\n📊 圖表模擬器")
    println("-"^15)
    
    # 檢查是否有圖表數據
    if !isdir("data_amibroker")
        println("❌ 找不到圖表數據目錄")
        return
    end
    
    println("圖表模擬器功能開發中...")
    println("請使用現有的圖表模擬器腳本：")
    println("  julia src/amibroker_chart_simple.jl")
end

"""
系統自檢
"""
function system_check()
    println("\n🔧 系統自檢")
    println("-"^10)
    
    # 檢查數據文件
    println("檢查數據文件...")
    if isfile("data/fan5.csv")
        println("✅ 彩票數據文件存在")
    else
        println("❌ 彩票數據文件不存在")
    end
    
    # 檢查圖表數據目錄
    if isdir("data_amibroker")
        println("✅ 圖表數據目錄存在")
    else
        println("❌ 圖表數據目錄不存在")
    end
    
    # 檢查繪圖包
    try
        @eval using Plots
        println("✅ Plots.jl 可用")
    catch
        println("⚠️  Plots.jl 不可用，僅支援文字圖表")
    end
    
    # 測試數據載入
    println("測試數據載入...")
    try
        data = load_real_data()
        if !isempty(data)
            println("✅ 數據載入成功 ($(length(data)) 期)")
        else
            println("❌ 數據載入失敗")
        end
    catch e
        println("❌ 數據載入錯誤：$e")
    end
end

"""
顯示號碼統計信息
"""
function display_number_stats(number::Int, stats::Dict{String,Any})
    println("\n號碼 $number 的統計資訊：")
    println("  出現次數：$(stats["appearances"]) / $(stats["total_draws"])")
    println("  出現頻率：$(stats["frequency"]) (理論值：$(stats["theoretical_frequency"]))")
    println("  當前 Skip：$(stats["current_skip"])")
    println("  理論 FFG：$(stats["theoretical_ffg"])")
    println("  實際 FFG：$(stats["empirical_ffg"])")
    println("  平均 Skip：$(stats["avg_skip"]) (範圍：$(stats["min_skip"])-$(stats["max_skip"]))")
    println("  冷熱評分：$(stats["heat_score"])/100")
    println("  時機評分：$(stats["timing_score"])/100")
    println("  是否有利：$(stats["is_favorable"] ? "是" : "否")")
    if stats["last_appearance_date"] !== nothing
        println("  上次開出：$(stats["last_appearance_date"])")
    end
end

"""
顯示號碼統計信息（範圍版本）
"""
function display_number_stats_range(number::Int, stats::Dict{String,Any})
    # 顯示分析範圍信息
    if haskey(stats, "analysis_range")
        range_info = stats["analysis_range"]
        println("\n📊 分析範圍信息：")
        if range_info["n_periods"] == range_info["actual_periods"]
            if range_info["n_periods"] == length(stats["total_draws"]) || range_info["start_date"] === nothing
                println("  範圍：全部數據 ($(range_info["actual_periods"]) 期)")
            else
                println("  範圍：最近 $(range_info["n_periods"]) 期")
                println("  日期範圍：$(range_info["start_date"]) 到 $(range_info["end_date"])")
            end
        else
            println("  請求期數：$(range_info["n_periods"])")
            println("  實際期數：$(range_info["actual_periods"])")
            println("  日期範圍：$(range_info["start_date"]) 到 $(range_info["end_date"])")
        end
    end

    # 顯示統計信息
    display_number_stats(number, stats)
end

"""
顯示有利號碼分析
"""
function display_favorable_analysis(historical_data::Vector{LotteryDraw})
    println("\n📈 有利號碼分析")
    println("="^50)

    favorable_numbers = find_favorable_numbers(historical_data, 15)

    println("排名  號碼  綜合評分  時機評分  冷熱評分  當前Skip  實際FFG")
    println("-"^60)

    for (rank, (number, score)) in enumerate(favorable_numbers)
        stats = analyze_number_statistics(number, historical_data)
        println("$(lpad(rank, 3))   $(lpad(number, 2))    $(lpad(round(score, digits=1), 5))    $(lpad(stats["timing_score"], 5))    $(lpad(stats["heat_score"], 5))     $(lpad(stats["current_skip"], 4))    $(lpad(stats["empirical_ffg"], 5))")
    end

    println("\n💡 建議重點關注前 10 名號碼")
    top_10 = [num for (num, _) in favorable_numbers[1:10]]
    println("重點號碼：$(join(lpad.(top_10, 2), " "))")
end

"""
顯示有利號碼分析（範圍版本）
"""
function display_favorable_analysis_range(historical_data::Vector{LotteryDraw}, selected_row::Int=1, n_periods::Union{Int,Nothing}=nothing)
    # 獲取範圍數據
    range_data = get_recent_data(historical_data, selected_row, n_periods)

    # 顯示分析範圍信息
    println("\n📊 分析範圍信息：")
    if n_periods === nothing
        println("  範圍：全部數據 ($(length(range_data)) 期)")
    else
        println("  範圍：最近 $n_periods 期")
        println("  實際期數：$(length(range_data))")
    end
    if !isempty(range_data)
        println("  日期範圍：$(range_data[end].draw_date) 到 $(range_data[1].draw_date)")
    end

    println("\n📈 有利號碼分析")
    println("="^50)

    favorable_numbers = find_favorable_numbers(range_data, 15)

    println("排名  號碼  綜合評分  時機評分  冷熱評分  當前Skip  實際FFG")
    println("-"^60)

    for (rank, (number, score)) in enumerate(favorable_numbers)
        stats = analyze_number_statistics(number, range_data)
        println("$(lpad(rank, 3))   $(lpad(number, 2))    $(lpad(round(score, digits=1), 5))    $(lpad(stats["timing_score"], 5))    $(lpad(stats["heat_score"], 5))     $(lpad(stats["current_skip"], 4))    $(lpad(stats["empirical_ffg"], 5))")
    end

    println("\n💡 建議重點關注前 10 名號碼")
    top_10 = [num for (num, _) in favorable_numbers[1:10]]
    println("重點號碼：$(join(lpad.(top_10, 2), " "))")

    # 與全部數據的比較
    if n_periods !== nothing && length(range_data) < length(historical_data)
        println("\n🔄 與全部數據比較：")
        all_favorable = find_favorable_numbers(historical_data, 10)
        all_top_10 = [num for (num, _) in all_favorable[1:10]]

        common_numbers = intersect(top_10, all_top_10)
        println("  共同熱門號碼：$(join(lpad.(common_numbers, 2), " ")) ($(length(common_numbers))/10)")

        new_numbers = setdiff(top_10, all_top_10)
        if !isempty(new_numbers)
            println("  近期新興號碼：$(join(lpad.(new_numbers, 2), " "))")
        end
    end
end

"""
生成 Wonder Grid 組合（簡化版）
"""
function generate_wonder_grid_combinations(key_number::Int, max_combinations::Int=50)::Vector{Vector{Int}}
    if !(1 <= key_number <= 39)
        throw(ArgumentError("關鍵號碼必須在 1-39 之間"))
    end
    
    combinations = Vector{Int}[]
    
    # 基於關鍵號碼生成 FFG 序列
    ffg_numbers = Int[]
    current = key_number
    
    for i in 1:15
        push!(ffg_numbers, current)
        current = (current + 7) % 39 + 1
    end
    
    ffg_numbers = sort(unique(ffg_numbers))
    
    # 生成組合
    if length(ffg_numbers) >= 5
        for i in 1:length(ffg_numbers)-4
            for j in i+1:length(ffg_numbers)-3
                for k in j+1:length(ffg_numbers)-2
                    for l in k+1:length(ffg_numbers)-1
                        for m in l+1:length(ffg_numbers)
                            combination = [ffg_numbers[i], ffg_numbers[j], ffg_numbers[k], ffg_numbers[l], ffg_numbers[m]]
                            push!(combinations, combination)
                            
                            if length(combinations) >= max_combinations
                                return combinations
                            end
                        end
                    end
                end
            end
        end
    end
    
    return combinations
end

"""
顯示組合
"""
function display_combinations(combinations::Vector{Vector{Int}}, title::String="Wonder Grid 組合")
    println("\n" * "="^50)
    println(title)
    println("="^50)
    
    for (i, combo) in enumerate(combinations[1:min(20, length(combinations))])
        println("$(lpad(i, 3)): $(join(lpad.(combo, 2), " "))")
    end
    
    if length(combinations) > 20
        println("... 還有 $(length(combinations) - 20) 個組合")
    end
    
    println("\n總共生成 $(length(combinations)) 個組合")
end

"""
Skip深度分析選單
"""
function skip_deep_analysis_menu()
    println("\n🧠 Skip深度分析")
    println("-"^18)

    historical_data = load_real_data()
    if isempty(historical_data)
        println("❌ 無法載入數據")
        return
    end

    println("總共有 $(length(historical_data)) 期數據可供分析")

    # 選擇分析類型
    println("\n請選擇深度分析類型：")
    println("1. 🔍 隱藏規律發現")
    println("2. 🔮 號碼趨勢預測")
    println("3. 🎯 選號策略優化")
    println("4. 🔬 交叉驗證分析")
    print("請選擇 (1-4): ")

    analysis_choice = strip(readline())

    if analysis_choice == "1"
        hidden_pattern_analysis(historical_data)
    elseif analysis_choice == "2"
        trend_prediction_analysis(historical_data)
    elseif analysis_choice == "3"
        strategy_optimization_analysis(historical_data)
    elseif analysis_choice == "4"
        cross_validation_analysis(historical_data)
    else
        println("❌ 無效選擇")
    end
end

"""
隱藏規律發現分析
"""
function hidden_pattern_analysis(historical_data::Vector{LotteryDraw})
    println("\n🔍 隱藏規律發現分析")
    println("-"^25)

    print("請輸入分析期數 (建議100-300期): ")
    periods_input = strip(readline())

    try
        periods = parse(Int, periods_input)
        if periods < 50
            println("❌ 期數太少，建議至少50期")
            return
        end

        max_periods = min(periods, length(historical_data) - 1)
        if max_periods != periods
            println("⚠️  調整分析期數為 $(max_periods) 期")
        end

        # 執行隱藏規律發現
        patterns = discover_hidden_patterns(historical_data, max_periods)

        if !isempty(patterns)
            println("\n📊 發現的隱藏規律：")
            println("="^60)

            for (i, pattern) in enumerate(patterns)
                println("$(i). $(pattern.pattern_type)")
                println("   描述：$(pattern.description)")
                println("   頻率：$(pattern.frequency) 次")
                println("   信心度：$(round(pattern.confidence * 100, digits=1))%")
                println("   預測價值：$(round(pattern.prediction_value, digits=1))")

                if !isempty(pattern.examples)
                    println("   示例：")
                    for (j, (period, date, skips)) in enumerate(pattern.examples[1:min(2, length(pattern.examples))])
                        skips_str = join([lpad(s, 2) for s in skips], " ")
                        println("     第$(period)期 ($(date))：Skip值 $(skips_str)")
                    end
                end
                println()
            end

            # 提供應用建議
            println("💡 應用建議：")
            high_confidence_patterns = [p for p in patterns if p.confidence > 0.6]
            if !isempty(high_confidence_patterns)
                println("  - 重點關注高信心度規律：$(length(high_confidence_patterns)) 個")
                for pattern in high_confidence_patterns[1:min(3, length(high_confidence_patterns))]
                    println("    * $(pattern.pattern_type)：$(pattern.description)")
                end
            else
                println("  - 當前數據中規律性較弱，建議增加分析期數或結合其他分析方法")
            end

        else
            println("❌ 未發現明顯的隱藏規律")
        end

    catch
        println("❌ 無效的期數輸入")
    end
end

"""
趨勢預測分析
"""
function trend_prediction_analysis(historical_data::Vector{LotteryDraw})
    println("\n🔮 號碼趨勢預測分析")
    println("-"^25)

    print("請輸入預測基礎期數 (建議50-150期): ")
    periods_input = strip(readline())

    try
        periods = parse(Int, periods_input)
        if periods < 30
            println("❌ 期數太少，建議至少30期")
            return
        end

        max_periods = min(periods, length(historical_data))
        if max_periods != periods
            println("⚠️  調整分析期數為 $(max_periods) 期")
        end

        # 執行趨勢預測
        trends = SkipPatternAnalysis.predict_number_trends(historical_data, max_periods)

        if !isempty(trends)
            println("\n📈 號碼趨勢預測結果：")
            println("="^80)
            println("號碼  當前Skip  預測Skip  趨勢方向  信心度  異常分數  週期長度")
            println("-"^80)

            # 顯示前20個最有信心的預測
            for trend in trends[1:min(20, length(trends))]
                cycle_str = trend.cycle_length === nothing ? "無" : string(trend.cycle_length)
                println("$(lpad(trend.number, 3))   $(lpad(trend.current_skip, 7))   $(lpad(round(trend.predicted_next_skip, digits=1), 7))   $(rpad(trend.trend_direction, 6))   $(lpad(round(trend.confidence_level * 100, digits=1), 5))%   $(lpad(round(trend.anomaly_score, digits=2), 7))    $(cycle_str)")
            end

            # 分類分析
            println("\n📊 趨勢分類統計：")
            up_trends = [t for t in trends if t.trend_direction == "上升"]
            down_trends = [t for t in trends if t.trend_direction == "下降"]
            stable_trends = [t for t in trends if t.trend_direction == "穩定"]

            println("  上升趨勢：$(length(up_trends)) 個號碼")
            println("  下降趨勢：$(length(down_trends)) 個號碼")
            println("  穩定趨勢：$(length(stable_trends)) 個號碼")

            # 高信心度預測
            high_confidence = [t for t in trends if t.confidence_level > 0.7]
            if !isempty(high_confidence)
                println("\n🎯 高信心度預測 (信心度>70%)：")
                for trend in high_confidence[1:min(10, length(high_confidence))]
                    println("  號碼 $(trend.number)：$(trend.trend_direction)趨勢，預測Skip=$(round(trend.predicted_next_skip, digits=1))")
                end
            end

            # 異常檢測
            high_anomaly = [t for t in trends if t.anomaly_score > 0.8]
            if !isempty(high_anomaly)
                println("\n⚠️  異常號碼檢測 (異常分數>0.8)：")
                for trend in high_anomaly
                    println("  號碼 $(trend.number)：當前Skip=$(trend.current_skip)，異常分數=$(round(trend.anomaly_score, digits=2))")
                end
            end

        else
            println("❌ 趨勢預測分析失敗")
        end

    catch
        println("❌ 無效的期數輸入")
    end
end

# 主程式入口
if abspath(PROGRAM_FILE) == @__FILE__
    main_menu()
end
