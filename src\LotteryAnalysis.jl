#!/usr/bin/env julia

"""
彩票分析模組
專門處理彩票號碼統計、配對分析和FFG計算
"""

module LotteryAnalysis

using Dates, CSV, DataFrames, Printf, Statistics

export LotteryDraw, SimpleFFGCalculator, PairFrequency, PairAnalysisResult
export analyze_number_statistics, find_favorable_numbers, calculate_ffg_median
export analyze_number_pairings, load_real_data, validate_data_integrity
export get_recent_data, analyze_number_statistics_range, find_favorable_numbers_range

# 基本數據結構
struct LotteryDraw
    numbers::Vector{Int}
    draw_date::Date
    draw_id::Int

    function LotteryDraw(numbers::Vector{Int}, draw_date::Date=today(), draw_id::Int=1)
        if length(numbers) != 5
            throw(ArgumentError("必須是 5 個號碼"))
        end
        if any(n -> n < 1 || n > 39, numbers)
            throw(ArgumentError("號碼必須在 1-39 之間"))
        end
        new(sort(numbers), draw_date, draw_id)
    end
end

# FFG 計算器
struct SimpleFFGCalculator
    degree_of_certainty::Float64

    function SimpleFFGCalculator(dc::Float64=0.5)
        if !(0.0 < dc < 1.0)
            throw(ArgumentError("確定度必須在 0 和 1 之間"))
        end
        new(dc)
    end
end

# 配對頻率數據結構
struct PairFrequency
    number1::Int
    number2::Int
    frequency_count::Int
    frequency_percentage::Float64
    last_appearance_date::Union{Date, Nothing}

    function PairFrequency(number1::Int, number2::Int, frequency_count::Int, 
                          frequency_percentage::Float64, last_appearance_date::Union{Date, Nothing}=nothing)
        if !(1 <= number1 <= 39) || !(1 <= number2 <= 39)
            throw(ArgumentError("號碼必須在 1-39 之間"))
        end
        
        if number1 > number2
            number1, number2 = number2, number1
        end
        
        if frequency_count < 0
            throw(ArgumentError("頻率計數不能為負數"))
        end
        
        if !(0.0 <= frequency_percentage <= 100.0)
            throw(ArgumentError("頻率百分比必須在 0-100 之間"))
        end
        
        new(number1, number2, frequency_count, frequency_percentage, last_appearance_date)
    end
end

# 配對分析結果容器
struct PairAnalysisResult
    total_pairs::Int
    analyzed_draws::Int
    pair_frequencies::Vector{PairFrequency}
    analysis_date::Date

    function PairAnalysisResult(total_pairs::Int, analyzed_draws::Int, 
                               pair_frequencies::Vector{PairFrequency}, analysis_date::Date=today())
        if total_pairs < 0 || analyzed_draws < 0
            throw(ArgumentError("總配對數和分析期數不能為負數"))
        end
        
        if isempty(pair_frequencies) && total_pairs > 0
            throw(ArgumentError("配對頻率數據不能為空"))
        end
        
        new(total_pairs, analyzed_draws, pair_frequencies, analysis_date)
    end
end

"""
計算理論 FFG 中位數
"""
function calculate_ffg_median(calc::SimpleFFGCalculator)::Float64
    p = 5.0 / 39.0  # 號碼出現機率
    return log(1 - calc.degree_of_certainty) / log(1 - p)
end

"""
計算號碼的 Skip 值（距離上次出現的期數）
"""
function calculate_skip(number::Int, historical_data::Vector{LotteryDraw})::Int
    for (i, draw) in enumerate(historical_data)
        if number in draw.numbers
            return i - 1
        end
    end
    return length(historical_data)
end

"""
計算號碼的所有 Skip 值序列
"""
function calculate_skip_sequence(number::Int, historical_data::Vector{LotteryDraw})::Vector{Int}
    skip_sequence = Int[]
    appearance_positions = Int[]

    for (i, draw) in enumerate(historical_data)
        if number in draw.numbers
            push!(appearance_positions, i)
        end
    end

    if length(appearance_positions) >= 2
        for i in 2:length(appearance_positions)
            skip = appearance_positions[i] - appearance_positions[i-1] - 1
            push!(skip_sequence, skip)
        end
    end

    return skip_sequence
end

"""
計算號碼的實際 FFG 中位數
"""
function calculate_empirical_ffg_median(number::Int, historical_data::Vector{LotteryDraw})::Float64
    skip_sequence = calculate_skip_sequence(number, historical_data)

    if isempty(skip_sequence)
        calc = SimpleFFGCalculator()
        return calculate_ffg_median(calc)
    end

    sorted_skips = sort(skip_sequence)
    n = length(sorted_skips)

    if n % 2 == 1
        return Float64(sorted_skips[div(n + 1, 2)])
    else
        return (sorted_skips[div(n, 2)] + sorted_skips[div(n, 2)+1]) / 2.0
    end
end

"""
分析號碼的統計資訊
"""
function analyze_number_statistics(number::Int, historical_data::Vector{LotteryDraw})::Dict{String,Any}
    if isempty(historical_data)
        return Dict("error" => "沒有歷史數據")
    end

    appearances = 0
    last_appearance = -1
    appearance_positions = Int[]

    for (i, draw) in enumerate(historical_data)
        if number in draw.numbers
            appearances += 1
            if last_appearance == -1
                last_appearance = i
            end
            push!(appearance_positions, i)
        end
    end

    total_draws = length(historical_data)
    frequency = appearances / total_draws
    current_skip = last_appearance == -1 ? total_draws : last_appearance - 1

    calc = SimpleFFGCalculator()
    theoretical_ffg = calculate_ffg_median(calc)
    empirical_ffg = calculate_empirical_ffg_median(number, historical_data)

    skip_sequence = calculate_skip_sequence(number, historical_data)
    avg_skip = isempty(skip_sequence) ? 0.0 : sum(skip_sequence) / length(skip_sequence)
    max_skip = isempty(skip_sequence) ? 0 : maximum(skip_sequence)
    min_skip = isempty(skip_sequence) ? 0 : minimum(skip_sequence)

    theoretical_expectation = 5.0 / 39.0
    heat_score = min(100, max(0, (frequency / theoretical_expectation) * 50))

    timing_score = if current_skip <= empirical_ffg
        min(100, (empirical_ffg - current_skip + 1) / empirical_ffg * 100)
    else
        max(0, 100 - (current_skip - empirical_ffg) / empirical_ffg * 50)
    end

    return Dict{String,Any}(
        "number" => number,
        "appearances" => appearances,
        "frequency" => round(frequency, digits=4),
        "theoretical_frequency" => round(theoretical_expectation, digits=4),
        "current_skip" => current_skip,
        "theoretical_ffg" => round(theoretical_ffg, digits=2),
        "empirical_ffg" => round(empirical_ffg, digits=2),
        "avg_skip" => round(avg_skip, digits=2),
        "max_skip" => max_skip,
        "min_skip" => min_skip,
        "skip_count" => length(skip_sequence),
        "heat_score" => round(heat_score, digits=1),
        "timing_score" => round(timing_score, digits=1),
        "is_favorable" => current_skip <= empirical_ffg,
        "total_draws" => total_draws,
        "last_appearance_date" => last_appearance > 0 ? historical_data[last_appearance].draw_date : nothing
    )
end

"""
獲取最近N期數據
selected_row: 選定的期數位置（1為最新期）
n_periods: 要分析的期數（默認為全部）
"""
function get_recent_data(historical_data::Vector{LotteryDraw}, selected_row::Int=1, n_periods::Union{Int,Nothing}=nothing)::Vector{LotteryDraw}
    if isempty(historical_data)
        return LotteryDraw[]
    end

    total_periods = length(historical_data)

    # 驗證 selected_row 範圍
    if selected_row < 1 || selected_row > total_periods
        throw(ArgumentError("選定期數 $selected_row 超出範圍 (1-$total_periods)"))
    end

    # 如果未指定期數，使用全部數據
    if n_periods === nothing
        return historical_data
    end

    # 驗證 n_periods 範圍
    if n_periods < 1
        throw(ArgumentError("分析期數必須大於 0"))
    end

    # 計算起始位置：從 selected_row 開始往後取 n_periods 期
    # 注意：數據是按時間倒序排列的（最新在前）
    start_row = selected_row
    end_row = min(total_periods, selected_row + n_periods - 1)

    # 返回指定範圍的數據
    return historical_data[start_row:end_row]
end

"""
找出最有利的號碼（優化版本）
"""
function find_favorable_numbers(historical_data::Vector{LotteryDraw}, top_n::Int=15)::Vector{Tuple{Int,Float64}}
    # 預分配向量以提高性能
    favorable_numbers = Vector{Tuple{Int,Float64}}(undef, 39)

    # 並行計算所有號碼的統計
    Threads.@threads for number in 1:39
        stats = analyze_number_statistics(number, historical_data)
        combined_score = stats["timing_score"] * 0.6 + stats["heat_score"] * 0.4
        favorable_numbers[number] = (number, combined_score)
    end

    # 使用部分排序優化
    sort!(favorable_numbers, by=x -> x[2], rev=true)
    return favorable_numbers[1:min(top_n, length(favorable_numbers))]
end

"""
基於指定期數範圍分析號碼統計（範圍版本）
"""
function analyze_number_statistics_range(number::Int, historical_data::Vector{LotteryDraw},
                                       selected_row::Int=1, n_periods::Union{Int,Nothing}=nothing)::Dict{String,Any}
    # 獲取指定範圍的數據
    range_data = get_recent_data(historical_data, selected_row, n_periods)

    # 使用範圍數據進行分析
    stats = analyze_number_statistics(number, range_data)

    # 添加範圍信息
    stats["analysis_range"] = Dict(
        "selected_row" => selected_row,
        "n_periods" => n_periods === nothing ? length(historical_data) : n_periods,
        "actual_periods" => length(range_data),
        "start_date" => isempty(range_data) ? nothing : range_data[end].draw_date,
        "end_date" => isempty(range_data) ? nothing : range_data[1].draw_date
    )

    return stats
end

"""
基於指定期數範圍找出最有利的號碼（範圍版本）
"""
function find_favorable_numbers_range(historical_data::Vector{LotteryDraw}, selected_row::Int=1,
                                    n_periods::Union{Int,Nothing}=nothing, top_n::Int=15)::Vector{Tuple{Int,Float64}}
    # 獲取指定範圍的數據
    range_data = get_recent_data(historical_data, selected_row, n_periods)

    # 使用範圍數據進行分析
    return find_favorable_numbers(range_data, top_n)
end

"""
從 CSV 文件載入真實開獎數據
"""
function load_real_data(filename::String="data/fan5.csv")::Vector{LotteryDraw}
    if !isfile(filename)
        println("❌ 找不到數據文件：$filename")
        return LotteryDraw[]
    end

    draws = LotteryDraw[]
    invalid_lines = 0

    try
        lines = readlines(filename)
        println("📊 載入 $(length(lines)) 期真實開獎數據...")

        for (i, line) in enumerate(lines)
            parts = split(strip(line), ',')
            if length(parts) >= 6
                try
                    date_str = parts[1]
                    draw_date = Date(date_str, "yyyy-mm-dd")
                    numbers = [parse(Int, parts[j]) for j in 2:6]

                    if all(n -> 1 <= n <= 39, numbers) && length(unique(numbers)) == 5
                        push!(draws, LotteryDraw(numbers, draw_date, i))
                    else
                        invalid_lines += 1
                    end
                catch e
                    invalid_lines += 1
                end
            else
                invalid_lines += 1
            end
        end

        if invalid_lines > 0
            println("⚠️  跳過 $invalid_lines 行無效數據")
        end

        reverse!(draws)
        for (i, draw) in enumerate(draws)
            draws[i] = LotteryDraw(draw.numbers, draw.draw_date, i)
        end

        println("✅ 成功載入 $(length(draws)) 期有效數據")
    catch e
        println("❌ 載入數據時發生錯誤：$e")
    end

    return draws
end

"""
驗證數據完整性
"""
function validate_data_integrity(historical_data::Vector{LotteryDraw})::Dict{String,Any}
    if isempty(historical_data)
        return Dict("valid" => false, "error" => "數據為空")
    end

    issues = String[]
    dates = [draw.draw_date for draw in historical_data]
    
    if !issorted(dates, rev=true)
        push!(issues, "日期順序不正確")
    end

    all_numbers = Int[]
    for draw in historical_data
        append!(all_numbers, draw.numbers)
        if length(draw.numbers) != 5
            push!(issues, "期號 $(draw.draw_id) 號碼數量不正確")
        end
        if any(n -> n < 1 || n > 39, draw.numbers)
            push!(issues, "期號 $(draw.draw_id) 號碼超出範圍")
        end
        if length(unique(draw.numbers)) != 5
            push!(issues, "期號 $(draw.draw_id) 有重複號碼")
        end
    end

    number_counts = Dict{Int,Int}()
    for num in all_numbers
        number_counts[num] = get(number_counts, num, 0) + 1
    end

    missing_numbers = [i for i in 1:39 if !haskey(number_counts, i)]
    if !isempty(missing_numbers)
        push!(issues, "號碼 $(missing_numbers) 從未出現")
    end

    return Dict{String,Any}(
        "valid" => isempty(issues),
        "issues" => issues,
        "total_draws" => length(historical_data),
        "date_range" => (historical_data[end].draw_date, historical_data[1].draw_date),
        "number_distribution" => number_counts
    )
end

end # module LotteryAnalysis
